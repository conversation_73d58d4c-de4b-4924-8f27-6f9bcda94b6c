import { SinonStub, stub, restore } from "sinon";
import { expect, should } from "chai";
import { SlackNotificationService } from "../../skywind/services/slackNotification";
import { BlockedDomainAlert, EmptyPoolAlert, DefaultDomainBlockedAlert } from "../../skywind/services/slackMessageFormatter";
import * as httpClientModule from "../../skywind/utils/httpClient";
import config from "../../skywind/config";

should();

class MockHttpClient {
    private sentMessages: any[] = [];

    async post<T>(url: string, data: any): Promise<T> {
        this.sentMessages.push({ url, data });
        return {} as T;
    }

    // Helper methods for testing
    getSentMessages() {
        return this.sentMessages;
    }

    getLastMessage() {
        return this.sentMessages[this.sentMessages.length - 1];
    }

    clear() {
        this.sentMessages = [];
    }
}

describe("SlackNotificationService", () => {
    let createHttpClientStub: SinonStub;
    let httpClient: MockHttpClient;
    let slackService: SlackNotificationService;
    let originalConfig: any;

    before(() => {
        originalConfig = {
            enabled: config.slackNotifications.enabled,
            webhookUrl: config.slackNotifications.blockedDomainsWebhookUrl
        };
        
        config.slackNotifications.enabled = true;
        config.slackNotifications.blockedDomainsWebhookUrl = "https://hooks.slack.com/test";
    });

    beforeEach(() => {
        createHttpClientStub = stub(httpClientModule, "createHttpClient");
        httpClient = new MockHttpClient();
        createHttpClientStub.returns(httpClient);
        slackService = new SlackNotificationService();
    });

    afterEach(() => {
        restore();
        httpClient.clear();
    });

    after(() => {
        config.slackNotifications.enabled = originalConfig.enabled;
        config.slackNotifications.blockedDomainsWebhookUrl = originalConfig.webhookUrl;
    });

    describe("sendBlockedDomainAlert", () => {
        it("should send blocked domain alert with all fields", async () => {
            const alert: BlockedDomainAlert = {
                domain: "example.com",
                blockedAt: new Date("2023-01-01T12:00:00Z"),
                poolId: 456,
                poolName: "Test Pool",
                reason: "Domain blocked by monitoring"
            };

            await slackService.sendBlockedDomainAlert(alert);

            const sentMessage = httpClient.getLastMessage();
            expect(sentMessage).to.not.be.undefined;
            expect(sentMessage.data.text).to.include("🚫 *Domain Blocked Alert*");
            expect(sentMessage.data.text).to.include("*Domain:* example.com");
            expect(sentMessage.data.text).to.include("*Blocked At:* 2023-01-01T12:00:00.000Z");
            expect(sentMessage.data.text).to.include("*Pool:* Test Pool (ID: 456)");
            expect(sentMessage.data.text).to.include("*Reason:* Domain blocked by monitoring");
            expect(sentMessage.data.channel).to.equal("#blocked_domains");
            expect(sentMessage.data.username).to.equal("Domain Monitor");
            expect(sentMessage.data.icon_emoji).to.equal(":warning:");
        });

        it("should send blocked domain alert with minimal fields", async () => {
            const alert: BlockedDomainAlert = {
                domain: "minimal.com",
                blockedAt: new Date("2023-01-01T12:00:00Z")
            };

            await slackService.sendBlockedDomainAlert(alert);

            const sentMessage = httpClient.getLastMessage();
            expect(sentMessage).to.not.be.undefined;
            expect(sentMessage.data.text).to.include("🚫 *Domain Blocked Alert*");
            expect(sentMessage.data.text).to.include("*Domain:* minimal.com");
            expect(sentMessage.data.text).to.include("*Blocked At:* 2023-01-01T12:00:00.000Z");
            expect(sentMessage.data.text).to.not.include("*Entity:*");
            expect(sentMessage.data.text).to.not.include("*Pool:*");
            expect(sentMessage.data.text).to.not.include("*Reason:*");
        });
    });

    describe("sendEmptyPoolAlert", () => {
        it("should send empty pool alert for static pool", async () => {
            const alert: EmptyPoolAlert = {
                poolId: 789,
                poolName: "Empty Static Pool",
                poolType: "static"
            };

            await slackService.sendEmptyPoolAlert(alert);

            const sentMessage = httpClient.getLastMessage();
            expect(sentMessage).to.not.be.undefined;
            expect(sentMessage.data.text).to.include("⚠️ *Empty Domain Pool Alert*");
            expect(sentMessage.data.text).to.include("*Pool:* Empty Static Pool (ID: 789)");
            expect(sentMessage.data.text).to.include("*Pool Type:* static");
            expect(sentMessage.data.text).to.include("*Status:* No active, unblocked domains available");
            expect(sentMessage.data.channel).to.equal("#blocked_domains");
            expect(sentMessage.data.username).to.equal("Domain Monitor");
            expect(sentMessage.data.icon_emoji).to.equal(":exclamation:");
        });

        it("should send empty pool alert for dynamic pool without entity", async () => {
            const alert: EmptyPoolAlert = {
                poolId: 999,
                poolName: "Empty Dynamic Pool",
                poolType: "dynamic"
            };

            await slackService.sendEmptyPoolAlert(alert);

            const sentMessage = httpClient.getLastMessage();
            expect(sentMessage).to.not.be.undefined;
            expect(sentMessage.data.text).to.include("⚠️ *Empty Domain Pool Alert*");
            expect(sentMessage.data.text).to.include("*Pool:* Empty Dynamic Pool (ID: 999)");
            expect(sentMessage.data.text).to.include("*Pool Type:* dynamic");
            expect(sentMessage.data.text).to.not.include("*Entity:*");
        });
    });

    describe("sendDefaultDomainBlockedAlert", () => {
        it("should send default domain blocked alert", async () => {
            const alert: DefaultDomainBlockedAlert = {
                domain: "default.example.com",
                blockedAt: new Date("2023-01-01T12:00:00Z"),
                domainType: "static"
            };

            await slackService.sendDefaultDomainBlockedAlert(alert);

            const sentMessage = httpClient.getLastMessage();
            expect(sentMessage).to.not.be.undefined;
            expect(sentMessage.data.text).to.include("🔴 *Default Domain Blocked Alert*");
            expect(sentMessage.data.text).to.include("*Domain:* default.example.com");
            expect(sentMessage.data.text).to.include("*Domain Type:* static");
            expect(sentMessage.data.text).to.include("*Blocked At:* 2023-01-01T12:00:00.000Z");
            expect(sentMessage.data.text).to.include("*Impact:* Default domain is no longer accessible");
            expect(sentMessage.data.channel).to.equal("#blocked_domains");
            expect(sentMessage.data.username).to.equal("Domain Monitor");
            expect(sentMessage.data.icon_emoji).to.equal(":rotating_light:");
        });
    });

    describe("when Slack notifications are disabled", () => {
        beforeEach(() => {
            config.slackNotifications.enabled = false;
            slackService = new SlackNotificationService();
        });

        it("should not send blocked domain alert when disabled", async () => {
            const alert: BlockedDomainAlert = {
                domain: "example.com",
                blockedAt: new Date()
            };

            await slackService.sendBlockedDomainAlert(alert);

            expect(httpClient.getSentMessages()).to.have.length(0);
        });

        it("should not send empty pool alert when disabled", async () => {
            const alert: EmptyPoolAlert = {
                poolId: 123,
                poolName: "Test Pool",
                poolType: "static"
            };

            await slackService.sendEmptyPoolAlert(alert);

            expect(httpClient.getSentMessages()).to.have.length(0);
        });

        it("should not send default domain blocked alert when disabled", async () => {
            const alert: DefaultDomainBlockedAlert = {
                domain: "default.com",
                blockedAt: new Date(),
                domainType: "static"
            };

            await slackService.sendDefaultDomainBlockedAlert(alert);

            expect(httpClient.getSentMessages()).to.have.length(0);
        });
    });

    describe("when webhook URL is not configured", () => {
        beforeEach(() => {
            config.slackNotifications.enabled = true;
            config.slackNotifications.blockedDomainsWebhookUrl = "";
            slackService = new SlackNotificationService();
        });

        it("should not send alerts when webhook URL is empty", async () => {
            const alert: BlockedDomainAlert = {
                domain: "example.com",
                blockedAt: new Date()
            };

            await slackService.sendBlockedDomainAlert(alert);

            expect(httpClient.getSentMessages()).to.have.length(0);
        });
    });
});
