import { measures } from "@skywind-group/sw-utils";
import { loadDomains } from "../services/domainWatcher/loadDomains";
import { CronJob } from "../utils/cronJob";
import config from "../config";
import { DomainWatcher, getDomainWatcher } from "../services/domainWatcher/domainWatcher";
import { Models } from "../models/models";
import { EntityStatus } from "../entities/entity";
import { getSlackNotificationService } from "../services/slackNotification";
import { EmptyPoolAlert } from "../services/slackMessageFormatter";
import logger from "../utils/logger";

const name = "domain-monitoring";
const log = logger(`job:${name}`);

export class DomainMonitoringJob {
    private readonly watchers = new Map<string, DomainWatcher>();
    private readonly slackService = getSlackNotificationService();

    public async fire() {
        await measures.measureProvider.runInTransaction("Domain monitoring job", async () => {
            try {
                const items = await loadDomains();
                if (items.size) {
                    for (const [adapterId, domains] of items.entries()) {
                        log.info({ domains: Object.fromEntries(domains) }, "Updating domains for adapter %s", adapterId);
                        await this.getWatcher(adapterId).update(domains, log);
                    }
                } else {
                    log.info("No domains to update");
                }

                // Check for empty domain pools
                await this.checkAndSendEmptyPoolAlerts();
            } catch (err) {
                measures.measureProvider.saveError(err);
                throw err;
            }
        });
    }

    private getWatcher(adapter: string) {
        let item = this.watchers.get(adapter);
        if (!item) {
            item = getDomainWatcher(adapter, log);
            this.watchers.set(adapter, item);
        }
        return item;
    }

    private async checkAndSendEmptyPoolAlerts(): Promise<void> {
        try {
            log.info("Checking for empty domain pools");

            // Check static domain pools
            await this.checkEmptyStaticPools();

            // Check dynamic domain pools
            await this.checkEmptyDynamicPools();
        } catch (error) {
            log.error({ error: error.message }, "Failed to check empty pool alerts");
        }
    }

    private async checkEmptyStaticPools(): Promise<void> {
        const staticPools = await Models.StaticDomainPoolModel.findAll({
            include: [{
                model: Models.StaticDomainModel,
                as: "domains",
                through: {
                    where: {
                        isActive: true,
                        blockedDate: null
                    },
                    attributes: []
                },
                where: {
                    status: "active"
                },
                required: false
            }]
        });

        for (const pool of staticPools) {
            if (!pool.domains || pool.domains.length === 0) {
                // Pool is empty, find entities using this pool
                const entitiesUsingPool = await Models.EntityModel.findAll({
                    where: {
                        staticDomainPoolId: pool.id,
                        status: EntityStatus.NORMAL
                    }
                });

                if (entitiesUsingPool.length > 0) {
                    const alert: EmptyPoolAlert = {
                        poolId: pool.id,
                        poolName: pool.name,
                        poolType: "static"
                    };

                    await this.slackService.sendEmptyPoolAlert(alert);
                }
            }
        }
    }

    private async checkEmptyDynamicPools(): Promise<void> {
        const dynamicPools = await Models.DynamicDomainPoolModel.findAll({
            include: [{
                model: Models.DynamicDomainModel,
                as: "domains",
                through: {
                    where: {
                        isActive: true,
                        blockedDate: null
                    },
                    attributes: []
                },
                where: {
                    status: "active"
                },
                required: false
            }]
        });

        for (const pool of dynamicPools) {
            if (!pool.domains || pool.domains.length === 0) {
                // Pool is empty, find entities using this pool
                const entitiesUsingPool = await Models.EntityModel.findAll({
                    where: {
                        dynamicDomainPoolId: pool.id,
                        status: EntityStatus.NORMAL
                    }
                });

                if (entitiesUsingPool.length > 0) {
                    const alert: EmptyPoolAlert = {
                        poolId: pool.id,
                        poolName: pool.name,
                        poolType: "dynamic"
                    };

                    await this.slackService.sendEmptyPoolAlert(alert);
                }
            }
        }
    }
}

const job = new DomainMonitoringJob();

let cronJob: CronJob;

export async function initDomainMonitoringJob(): Promise<void> {
    if (config.domainMonitoring.cronJob.enabled && !cronJob) {
        const { schedule, timeout, runOnServerStart } = config.domainMonitoring.cronJob;
        cronJob = new CronJob({
            name,
            schedule,
            timeout
        }, job.fire.bind(job));

        if (runOnServerStart) {
            cronJob.invoke.bind(job);
            await cronJob.invoke();
        }
    }
}
