import { DynamicDomainPoolDBInstance, } from "../models/dynamicDomainPool";
import { Models } from "../models/models";
import {
    DynamicDomainPoolService,
    DynamicDomainPoolAttributes,
    DynamicDomainPoolItemAttributes, DomainPoolCreateData
} from "../entities/domainPool";
import { sequelize as db } from "../storage/db";
import * as Errors from "../errors";
import * as DynamicDomainPoolCache from "../cache/dynamicDomainPoolCache";
import { FindOptions, Op, Transaction } from "sequelize";
import { lazy } from "@skywind-group/sw-utils";
import { BaseEntity, EntityWithChild, MIGRATION_STATUS } from "../entities/entity";
import { DynamicDomain } from "../entities/domain";
import { ApplicationLock, ApplicationLockId } from "../utils/applicationLock";
import EntityCache from "../cache/entity";
import MigrationService from "./migrationService";

const DynamicDomainPoolModel = Models.DynamicDomainPoolModel;
const DynamicDomainPoolItemModel = Models.DynamicDomainPoolItemModel;
const DynamicDomainModelInstance = Models.DynamicDomainModel;
const EntityModel = Models.EntityModel;

async function validateDynamicDomainsExist(domains: { id: number }[], transaction: Transaction) {
    const domainIds = domains.map(({ id }) => id);
    validateDomainsExist(domainIds, (await DynamicDomainModelInstance.findAll({
        where: { id: { [Op.in]: domainIds } },
        transaction
    })).map<number>(item => item.id));
}

function validateDomainsExist(domainIds: number[], foundDomainIds: number[]) {
    const foundIds = new Set(foundDomainIds);
    const missingIds = domainIds.filter(id => !foundIds.has(id));
    if (missingIds.length > 0) {
        throw new Errors.DomainNotFoundError(missingIds[0]);
    }
}

function toInfo(domainPool: DynamicDomainPoolDBInstance): DynamicDomainPoolAttributes {

    const { domains, ...result } = domainPool.toJSON();

    return {
        ...result,
        domains: domains?.map(({ DynamicDomainPoolItem, ...domain }) => {
            if (DynamicDomainPoolItem) {
                domain.isActive = DynamicDomainPoolItem.isActive;
                domain.blockedDate = DynamicDomainPoolItem.blockedDate;
            }
            return domain;
        }) || []
    };
}

class DynamicDomainPoolServiceImpl implements DynamicDomainPoolService {
    public async create(data: DomainPoolCreateData): Promise<DynamicDomainPoolAttributes> {
        return db.transaction(async transaction => {
            const existing = await DynamicDomainPoolModel.findOne(
                {
                    where: {
                        name: data.name
                    },
                    transaction
                }
            );

            if (existing) {
                throw new Errors.DomainPoolAlreadyExistsError();
            }

            const created = await DynamicDomainPoolModel.create({
                name: data.name,
                domainWatcherAdapterId: data.domainWatcherAdapterId
            }, { transaction });
            const dynamicDomainPoolId = created.id;

            if (data.domains && data.domains.length > 0) {
                await validateDynamicDomainsExist(data.domains, transaction);
                await DynamicDomainPoolItemModel.bulkCreate(data.domains.map(domain => ({
                    dynamicDomainPoolId,
                    dynamicDomainId: domain.id,
                    isActive: domain.isActive ?? true
                })), { transaction });
            }

            const domainPoolWithAssociations = await DynamicDomainPoolModel.findByPk(dynamicDomainPoolId, {
                include: {
                    model: DynamicDomainModelInstance,
                    as: "domains",
                    through: {
                        attributes: ["isActive", "blockedDate"]
                    }
                },
                transaction
            });

            return toInfo(domainPoolWithAssociations);
        });
    }

    public async update(dynamicDomainPoolId: number,
                        data: Partial<DomainPoolCreateData>): Promise<DynamicDomainPoolAttributes> {
        return db.transaction(async transaction => {
            const domainPool = await DynamicDomainPoolModel.findByPk(dynamicDomainPoolId, {
                include: {
                    model: DynamicDomainModelInstance,
                    as: "domains",
                    through: {
                        attributes: ["isActive", "blockedDate"]
                    }
                },
                transaction
            });
            if (!domainPool) {
                throw new Errors.DomainPoolNotFoundError();
            }

            if (data?.name || data?.domainWatcherAdapterId !== undefined) {
                const updateData: any = {};
                if (data?.name) {
                    updateData.name = data.name;
                }
                if (data?.domainWatcherAdapterId !== undefined) {
                    updateData.domainWatcherAdapterId = data.domainWatcherAdapterId;
                }
                await domainPool.update(updateData, { transaction });
            }

            if (data?.domains) {
                await validateDynamicDomainsExist(data.domains, transaction);

                await DynamicDomainPoolItemModel.destroy({ where: { dynamicDomainPoolId }, transaction });
                await DynamicDomainPoolItemModel.bulkCreate(data.domains.map(domain => ({
                    dynamicDomainPoolId,
                    dynamicDomainId: domain.id,
                    isActive: domain.isActive ?? true
                })), { transaction });
                const domains = domainPool.domains;
                const previousEnvironments = domains.map(d => d.environment);
                const newEnvironments = data.domains.map(d => d.environment);
                if (newEnvironments.some(env => !previousEnvironments.includes(env)) ||
                    previousEnvironments.some(env => !newEnvironments.includes(env))
                ) {
                    const entities = await EntityModel.findAll({
                        where: {
                            dynamicDomainPoolId
                        },
                    });
                    for (const dbEntity of entities) {
                        const entity = await EntityCache.findById(dbEntity.id);
                        const domains = domainPool.domains;
                        await this.triggerMigration(
                            entity,
                            domains,
                            transaction
                        );
                    }
                }
            }

            const domainPoolWithAssociations = await DynamicDomainPoolModel.findByPk(dynamicDomainPoolId, {
                include: {
                    model: DynamicDomainModelInstance,
                    as: "domains",
                    through: {
                        attributes: ["isActive", "blockedDate"]
                    }
                },
                transaction
            });

            const info = toInfo(domainPoolWithAssociations);
            DynamicDomainPoolCache.reset(dynamicDomainPoolId);

            return info;
        });
    }

    public async findById(id: number): Promise<DynamicDomainPoolAttributes> {
        const domainPool = await DynamicDomainPoolCache.findOne(id);
        if (!domainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }

        return toInfo(domainPool);
    }

    public async findAll(findOptions?: FindOptions<DynamicDomainPoolDBInstance>): Promise<DynamicDomainPoolAttributes[]> {
        const domainPools = await DynamicDomainPoolModel.findAll({
            ...findOptions,
            include: {
                model: DynamicDomainModelInstance,
                as: "domains",
                through: {
                    attributes: ["isActive", "blockedDate"]
                }
            }
        });
        return domainPools.map(toInfo);
    }

    public async remove(id: number): Promise<void> {
        const domainPool = await DynamicDomainPoolModel.findByPk(id);
        if (!domainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }
        await DynamicDomainPoolModel.destroy({ where: { id } });
        DynamicDomainPoolCache.reset(id);
    }

    public async addDomain(dynamicDomainPoolId: number,
                           dynamicDomainId: number): Promise<DynamicDomainPoolItemAttributes> {
        const dynamicDomainPool = await DynamicDomainPoolModel.findByPk(
            dynamicDomainPoolId,
            {
                include: {
                    model: DynamicDomainModelInstance,
                    as: "domains",
                    through: {
                        attributes: ["isActive", "blockedDate"]
                    }
                }
            }
        );
        if (!dynamicDomainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }

        const dynamicDomain = await DynamicDomainModelInstance.findByPk(dynamicDomainId);
        if (!dynamicDomain) {
            throw new Errors.DomainNotFoundError(dynamicDomainId);
        }

        let item = await DynamicDomainPoolItemModel.findOne({
            where: {
                dynamicDomainId,
                dynamicDomainPoolId
            }
        });
        if (item) {
            throw new Errors.DomainPoolItemAlreadyExistsError();
        }

        await db.transaction(async (transaction) => {
            const existingEnvironments = dynamicDomainPool.domains.map(d => d.environment);
            item = await DynamicDomainPoolItemModel.create({
                dynamicDomainId,
                dynamicDomainPoolId
            }, { transaction });
            // If after the update there are pool items which don't have
            // the environment of the new domain, trigger migration
            if (existingEnvironments.some(env => env !== dynamicDomain.environment)) {
                const entities = await EntityModel.findAll({
                    where: {
                        dynamicDomainPoolId
                    },
                    transaction
                });
                for (const dbEntity of entities) {
                    const entity = await EntityCache.findById(dbEntity.id);
                    const domains = dynamicDomainPool.domains;
                    await this.triggerMigration(
                        entity,
                        domains,
                        transaction
                    );
                }
            }
            DynamicDomainPoolCache.reset(dynamicDomainPoolId);
        });
        return item.toJSON();
    }

    public async removeDomain(dynamicDomainPoolId: number, dynamicDomainId: number): Promise<void> {
        const dynamicDomainPool = await DynamicDomainPoolModel.findByPk(
            dynamicDomainPoolId,
            {
                include: {
                    model: DynamicDomainModelInstance,
                    as: "domains",
                    through: {
                        attributes: ["isActive", "blockedDate"]
                    }
                }
            }
        );
        if (!dynamicDomainPool) {
            throw new Errors.DomainPoolNotFoundError();
        }

        const domainPoolItem = await DynamicDomainPoolItemModel.findOne({
            where: {
                dynamicDomainId,
                dynamicDomainPoolId
            }
        });
        if (!domainPoolItem) {
            throw new Errors.DomainPoolItemNotFoundError();
        }

        const dynamicDomain = await DynamicDomainModelInstance.findByPk(dynamicDomainId);
        if (!dynamicDomain) {
            throw new Errors.DomainNotFoundError(dynamicDomainId);
        }

        await db.transaction(async (transaction) => {
            await DynamicDomainPoolItemModel.destroy({
                where: {
                    dynamicDomainId,
                    dynamicDomainPoolId
                },
                transaction
            });
            const environmentsAfterUpdate = dynamicDomainPool.domains
                .filter(d => d.id !== domainPoolItem.dynamicDomainId)
                .map(d => d.environment);
            // If after the update none of the elements from the pool of domains has the environment
            // of the removed one, trigger migration
            if (!environmentsAfterUpdate.some(env => env === dynamicDomain.environment)) {
                const entities = await EntityModel.findAll({
                    where: {
                        dynamicDomainPoolId
                    },
                    transaction
                });
                for (const dbEntity of entities) {
                    const entity = await EntityCache.findById(dbEntity.id);
                    const domains = dynamicDomainPool.domains;
                    await this.triggerMigration(
                        entity,
                        domains,
                        transaction
                    );
                }
            }
            DynamicDomainPoolCache.reset(dynamicDomainPoolId);
        });
    }

    public disableDomain(poolId: number, domainId: number): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, { isActive: false });
    }

    public enableDomain(poolId: number, domainId: number): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, { isActive: true });
    }

    public setBlocked(poolId: number, domainId: number, blockedDate?: Date | null): Promise<void> {
        return this.updatePoolItemStatus(poolId, domainId, { blockedDate: blockedDate });
    }

    public countDomains(poolId: number, filter?: { environment?: string }): Promise<number> {
        const domainWhere: any = {
            status: "active"
        };

        if (filter?.environment) {
            domainWhere.environment = filter.environment;
        }

        return DynamicDomainPoolItemModel.count({
            include: [{
                model: DynamicDomainModelInstance,
                where: domainWhere,
                required: true
            }],
            where: {
                dynamicDomainPoolId: poolId,
                isActive: true,
                blockedDate: null
            }
        });
    }

    private async updatePoolItemStatus(dynamicDomainPoolId: number, dynamicDomainId: number, values: { isActive?: boolean; blockedDate?: Date | null }) {
        const dynamicDomain = await DynamicDomainModelInstance.findByPk(dynamicDomainId);
        if (!dynamicDomain) {
            throw new Errors.DomainNotFoundError(dynamicDomainId);
        }

        const existing = await DynamicDomainPoolItemModel.findOne({
            where: {
                dynamicDomainId,
                dynamicDomainPoolId
            }
        });
        if (!existing) {
            throw new Errors.DomainPoolItemNotFoundError();
        }

        await DynamicDomainPoolItemModel.update(
            values,
            {
                where: {
                    dynamicDomainId,
                    dynamicDomainPoolId
                }
            }
        );
        DynamicDomainPoolCache.reset(dynamicDomainPoolId);
    }

    private async triggerMigration(
        entity: BaseEntity,
        domains: DynamicDomain[],
        transaction: Transaction
    ): Promise<void> {
        const updatedEntities: number[] = [];
        await ApplicationLock.lock(transaction, ApplicationLockId.UPDATE_DYNAMIC_DOMAIN_POOL);
        if (entity.isBrand()) {
            await Models.EntityModel.update(
                {
                    migrationStatus: MIGRATION_STATUS.STARTED,
                } as any,
                { where: { id: entity.id }, transaction }
            );
            updatedEntities.push(entity.id);
        } else {
            await this.markChildrenForProgressingMigration(entity, updatedEntities, transaction);
        }

        EntityCache.reset();
        for (const id of updatedEntities) {
            await MigrationService.startMigration(id, domains, transaction);
        }
    }

    private async markChildrenForProgressingMigration(
        entity: BaseEntity,
        updatedEntities: number[],
        transaction: Transaction
    ): Promise<void> {

        const entityWithChild = entity as EntityWithChild;
        if (entityWithChild.child) {
            for (const child of entityWithChild.child) {
                if (child.migrationStatus === MIGRATION_STATUS.STARTED || child.migrationStatus === MIGRATION_STATUS.PROCESSING) {
                    return Promise.reject(new Errors.MigrationIsInProgressError());
                }
                if (!child.dynamicDomainPoolId) {
                    if (child.isBrand()) {
                        await Models.EntityModel.update({
                                migrationStatus: MIGRATION_STATUS.STARTED,
                            } as any,
                            { where: { id: child.id }, transaction });
                        updatedEntities.push(child.id);
                    } else {
                        await this.markChildrenForProgressingMigration(
                            child, updatedEntities, transaction);
                    }
                }
            }
        }
    }
}

const dynamicDomainPoolService = lazy<DynamicDomainPoolService>(() => new DynamicDomainPoolServiceImpl());

export const getDynamicDomainPoolService = () => dynamicDomainPoolService.get();
